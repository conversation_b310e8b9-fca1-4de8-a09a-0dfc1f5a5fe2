{"_id": {"$oid": "67a5b3505bf6b204fb931284"}, "timestamp": "2025-02-07T07:16:32.376528719Z", "event_type": "PNR_V3", "operation": "FLIGHT_SCHEDULE_CHANGE_CLOSED", "payload": {"version": 8, "createdVia": "OBT", "initialVersionCreatedVia": "OBT", "sourceInfo": {"sourcePnrId": "SUTABO", "bookingSource": "SABRE", "thirdParty": "SABRE", "bookingDateTime": {"iso8601": "2025-01-19T12:56:17Z"}, "posDescriptor": "1ZSK", "iataNumber": ""}, "invoiceDelayedBooking": false, "travelers": [{"user": {"dob": {"iso8601": "1993-01-01"}, "email": "<EMAIL>", "gender": "MALE", "name": {"family1": "Test", "family2": "", "given": "Integration", "middle": "", "preferred": ""}, "paymentInfos": [{"applicableTo": [], "card": {"id": "ea624176-8fac-4b9c-bf88-872fcd7588b3", "type": "CREDIT", "company": "AMEX", "name": "<PERSON>", "address": {"addressLines": ["123 Elm st"], "postalCode": "98199", "regionCode": "US"}, "number": "3XXXXXXXXXXX0005", "expiryMonth": 1, "expiryYear": 2039, "cvv": "", "label": "Stripe Amex Test Card"}, "accessType": "PERSONAL", "access": {"accessType": "PERSONAL", "entityIds": ["e05ce264-a591-4ab5-a01e-e4ab84f45408"], "entities": [{"entityId": "e05ce264-a591-4ab5-a01e-e4ab84f45408", "centralCardAccessLevel": "UNKNOWN"}]}}], "phoneNumbers": [{"countryCode": 1, "countryCodeSource": "UNSPECIFIED", "extension": "", "isoCountryCode": "US", "italianLeadingZero": false, "nationalNumber": 0, "numberOfLeadingZeros": 0, "preferredDomesticCarrierCode": "", "rawInput": "**********", "type": "MOBILE"}]}, "userBusinessInfo": {"designation": "", "email": "<EMAIL>", "employeeId": "", "legalEntityId": {"id": "96dd735a-d576-4e68-ad91-5e06da377e2e"}, "organizationId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"}}, "userOrgId": {"organizationAgencyId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"}, "organizationId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"}, "userId": {"id": "e05ce264-a591-4ab5-a01e-e4ab84f45408"}, "tmcInfo": {"id": {"id": "ecc5b835-8001-430c-98f8-fedeccebe4cf"}, "primaryServiceProviderTmc": {"tmcId": {"id": "ecc5b835-8001-430c-98f8-fedeccebe4cf"}}, "secondaryServiceProviderTmcs": [], "partnerTmcId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"}}, "tmcBasicInfo": {"contractingTmc": {"id": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"}}, "bookingTmc": {"id": {"id": "ecc5b835-8001-430c-98f8-fedeccebe4cf"}}}}, "persona": "EMPLOYEE", "isActive": false, "tier": "BASIC"}], "pnrTravelers": [{"userId": {"id": "e05ce264-a591-4ab5-a01e-e4ab84f45408"}, "travelerInfo": {"userId": {"id": "e05ce264-a591-4ab5-a01e-e4ab84f45408"}, "adhocTravelerInfo": {}}, "personalInfo": {"dob": {"iso8601": "1993-01-01"}, "email": "<EMAIL>", "gender": "MALE", "name": {"family1": "Test", "family2": "", "given": "Integration", "middle": "", "preferred": ""}, "phoneNumbers": [{"countryCode": 1, "countryCodeSource": "UNSPECIFIED", "extension": "", "isoCountryCode": "US", "italianLeadingZero": false, "nationalNumber": 0, "numberOfLeadingZeros": 0, "preferredDomesticCarrierCode": "", "rawInput": "**********", "type": "MOBILE"}]}, "loyalties": [], "persona": "EMPLOYEE", "businessInfo": {"legalEntity": {"id": "96dd735a-d576-4e68-ad91-5e06da377e2e", "name": "Otto Trip, Inc.", "ein": "", "externalId": "", "companySpecifiedAttributes": []}, "companyId": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"}, "companyInfo": {"id": {"id": "4ecabb34-5eb3-4192-a1c8-c634a151dc41"}, "name": "<PERSON>", "externalId": ""}}, "tier": "BASIC"}], "costOfGoodsSold": {"payments": [{"travelerIndices": [0], "userIds": [{"id": "e05ce264-a591-4ab5-a01e-e4ab84f45408"}], "fop": {"type": "CARD", "card": {"id": "ea624176-8fac-4b9c-bf88-872fcd7588b3", "type": "CREDIT", "company": "AMEX", "name": "<PERSON>", "address": {"addressLines": ["123 Elm st"], "administrativeArea": "WA", "administrativeAreaName": "", "description": "Billing Address", "isDefault": false, "languageCode": "", "locality": "Seattle", "locationCode": "", "organization": "", "postalCode": "98199", "continentCode": "", "recipients": [], "regionCode": "US", "regionName": "", "revision": 0, "sortingCode": "", "sublocality": "", "timezone": ""}, "number": "3XXXXXXXXXXX0005", "expiryMonth": 1, "expiryYear": 2039, "cvv": "", "label": "Stripe Amex Test Card", "currency": "", "externalId": "", "vaultId": "00000000-0000-0000-0000-000000000000", "expiry": {}}, "additionalInfo": "", "accessType": {"accessType": "PERSONAL", "entityIds": ["e05ce264-a591-4ab5-a01e-e4ab84f45408"], "entities": [{"entityId": "e05ce264-a591-4ab5-a01e-e4ab84f45408", "centralCardAccessLevel": "UNKNOWN"}]}, "paymentMethod": "CREDIT_CARD", "paymentMetadata": {"cardMetadata": {"card": {"id": "ea624176-8fac-4b9c-bf88-872fcd7588b3", "type": "CREDIT", "company": "AMEX", "name": "<PERSON>", "address": {"addressLines": ["123 Elm st"], "administrativeArea": "WA", "administrativeAreaName": "", "description": "Billing Address", "isDefault": false, "languageCode": "", "locality": "Seattle", "locationCode": "", "organization": "", "postalCode": "98199", "continentCode": "", "recipients": [], "regionCode": "US", "regionName": "", "revision": 0, "sortingCode": "", "sublocality": "", "timezone": ""}, "number": "3XXXXXXXXXXX0005", "expiryMonth": 1, "expiryYear": 2039, "cvv": "", "label": "Stripe Amex Test Card", "currency": "", "externalId": "", "vaultId": "00000000-0000-0000-0000-000000000000", "expiry": {}}, "accessType": {"accessType": "PERSONAL", "entityIds": ["e05ce264-a591-4ab5-a01e-e4ab84f45408"], "entities": [{"entityId": "e05ce264-a591-4ab5-a01e-e4ab84f45408", "centralCardAccessLevel": "UNKNOWN"}]}, "isLodgeCard": false}}, "paymentSourceType": "CARD"}, "paymentReference": "", "paymentType": "FLIGHTS", "paymentThirdParty": "UNKNOWN_PARTY", "paymentId": "", "paymentGateway": "PAYMENT_GATEWAY_UNKNOWN", "isRefunded": false, "networkTransactionId": ""}]}, "isFinalized": true, "policyInfo": {"outOfPolicy": false, "reasonCode": "UNKNOWN_CHECKOUT_ANSWER_TYPE", "reason": "", "appliedPolicyInfo": {"policies": [{"id": "366011aa-9ad6-4cab-bb6b-add9ef060c33", "version": "0"}], "ruleResultInfos": []}}, "airPnr": {"legs": [{"flights": [{"departureDateTime": {"iso8601": "2025-04-03T06:10:00"}, "arrivalDateTime": {"iso8601": "2025-04-03T08:59:00"}, "duration": {"iso8601": "PT2H49M"}, "flightId": "CgNTRUESA0xBWBoVChMyMDI1LTA0LTAzVDA2OjEwOjAwIhUKEzIwMjUtMDQtMDNUMDg6NTk6MDA=", "origin": "SEA", "destination": "LAX", "departureGate": {"gate": "", "terminal": ""}, "arrivalGate": {"gate": "", "terminal": ""}, "marketing": {"num": "6420", "airlineCode": "AA"}, "operating": {"num": "6420", "airlineCode": "AA"}, "operatingAirlineName": "SKYWEST AIRLINES AS AMERICAN EAGLE", "hiddenStops": [], "vendorConfirmationNumber": "SWQDSY", "cabin": "ECONOMY", "bookingCode": "N", "flightStatus": "CONFIRMED", "otherStatuses": [], "co2EmissionDetail": {"emissionValue": 0.21548499330999998, "averageEmissionValue": 0.21548499330999998, "flightDistanceKm": 1533.70102, "isApproximate": true}, "restrictions": [], "sourceStatus": "HK", "equipment": {"code": "CR7", "type": "", "name": "Canadair Regional Jet 700 | Regional Jet 550"}, "distance": {"length": 953, "unit": "MILE"}, "flightWaiverCodes": [], "amenities": [{}, {}, {}, {}, {}, {}, {}]}], "brandName": "Main Cabin", "validatingAirlineCode": "AA", "legStatus": "CONFIRMED_STATUS", "sortingPriority": 0, "travelerRestrictions": [], "fareOffers": [{"userId": {"id": "e05ce264-a591-4ab5-a01e-e4ab84f45408"}, "baggagePolicy": {"checkedIn": [{"description": "1 checked bag, 23 kgs (40 USD)"}, {"description": "+1 checked bag, 23 kgs (45 USD)"}], "carryOn": [{"description": "1 carry-on bag"}]}}, {"userId": {"id": "e05ce264-a591-4ab5-a01e-e4ab84f45408"}, "cancellationPolicy": {"description": "Non-refundable"}}, {"userId": {"id": "e05ce264-a591-4ab5-a01e-e4ab84f45408"}, "exchangePolicy": {"description": "Change allowed for free"}}], "legId": "CgNTRUESA0xBWBoKMjQ4OTIzNTM0Mg==", "rateType": "PUBLISHED", "preferredTypes": [], "preferences": []}, {"flights": [{"departureDateTime": {"iso8601": "2025-04-12T15:24:00"}, "arrivalDateTime": {"iso8601": "2025-04-12T18:32:00"}, "duration": {"iso8601": "PT3H8M"}, "flightId": "CgNMQVgSA1NFQRoVChMyMDI1LTA0LTEyVDE1OjI0OjAwIhUKEzIwMjUtMDQtMTJUMTg6MzI6MDA=", "origin": "LAX", "destination": "SEA", "departureGate": {"gate": "", "terminal": ""}, "arrivalGate": {"gate": "", "terminal": ""}, "marketing": {"num": "6462", "airlineCode": "AA"}, "operating": {"num": "6462", "airlineCode": "AA"}, "operatingAirlineName": "SKYWEST AIRLINES AS AMERICAN EAGLE", "hiddenStops": [], "vendorConfirmationNumber": "SWQDSY", "cabin": "ECONOMY", "bookingCode": "Q", "flightStatus": "CONFIRMED", "otherStatuses": [], "co2EmissionDetail": {"emissionValue": 0.21548499330999998, "averageEmissionValue": 0.21548499330999998, "flightDistanceKm": 1533.70102, "isApproximate": true}, "restrictions": [], "sourceStatus": "HK", "equipment": {"code": "CR7", "type": "", "name": "Canadair Regional Jet 700 | Regional Jet 550"}, "distance": {"length": 953, "unit": "MILE"}, "flightWaiverCodes": [], "amenities": [{}, {}, {}, {}, {}, {}, {}]}], "brandName": "Main Cabin", "validatingAirlineCode": "AA", "legStatus": "CONFIRMED_STATUS", "sortingPriority": 0, "travelerRestrictions": [], "fareOffers": [{"userId": {"id": "e05ce264-a591-4ab5-a01e-e4ab84f45408"}, "baggagePolicy": {"checkedIn": [{"description": "1 checked bag, 23 kgs (40 USD)"}], "carryOn": [{"description": "1 carry-on bag"}]}}, {"userId": {"id": "e05ce264-a591-4ab5-a01e-e4ab84f45408"}, "cancellationPolicy": {"description": "Non-refundable"}}, {"userId": {"id": "e05ce264-a591-4ab5-a01e-e4ab84f45408"}, "exchangePolicy": {"description": "Change allowed for free"}}], "legId": "CgNMQVgSA1NFQRoKMjQ4OTIzNTM0Mg==", "rateType": "PUBLISHED", "preferredTypes": [], "preferences": []}], "airPnrRemarks": [{"remarkString": "OTTO TRIP, INC."}, {"remarkString": "AUTH-AMEX/AX0005/19JAN/01391737291411029761       "}, {"remarkString": "HTL-RATECODE-THR"}, {"remarkString": "TRIPFEE-BC0.00/EXCX0.00"}, {"remarkString": "S*UD3 3984143638"}, {"remarkString": "ISPASSIVEPNR FALSE"}, {"remarkString": "  AUTH-<PERSON>VS BILLING ADDRESS AND ZIP POSTAL ARE MATC"}, {"remarkString": "S*UD78 IN POLICY"}, {"remarkString": "POSSIBLE DUPE BOOKING. SEE PNR OLVNZW OLWRSL"}, {"remarkString": "BOOKEDBYORGID 4ECABB34-5EB3-4192-A1C8-C634A151DC41"}, {"remarkString": "OBT-COMMISSION-PROCESSED-1737291398"}, {"remarkString": "2-SABREPROFILES¥OTTO TRIP INC."}, {"remarkString": "S*HU"}, {"remarkString": "PHONESPOTNANA"}, {"remarkString": "OBT-COMMISSION-SUCCESS-1737291398"}, {"remarkString": "MAILBOXSPOTNANA"}, {"remarkString": "XXAUTH/Y86286/AX3----------0005/AA/USD251.96/19JAN/S"}, {"remarkString": "CURR-USD"}, {"remarkString": "S*UD166 SPOTNANA"}, {"remarkString": "11 BROOKE DR"}, {"remarkString": "WORKFLOWID C7301F71CF56097E"}, {"remarkString": "HTL-RATECODE-WTH"}, {"remarkString": "BOOKEDBYUSERID 2A8658AD-0572-480C-BBC0-B8BD3B3FB78C"}, {"remarkString": "TRAVELERORGID 4ECABB34-5EB3-4192-A1C8-C634A151DC41"}, {"remarkString": "*AX3XXXXXXXXXX0005¥01/39-XN"}, {"remarkString": "TRIPID 3984143638"}, {"remarkString": "S*UD212 OTTO TRIP INC."}, {"remarkString": "NOVATO CA US 94947"}, {"remarkString": "S*ICSPOTNANA"}, {"remarkString": "PNRTYPE AIR"}, {"remarkString": "XXTAW/"}, {"remarkString": "ENVIRONMENT SBOXMETA"}, {"remarkString": "AA-TCC859996"}, {"remarkString": "HTL-RATECODE-SIG"}, {"remarkString": "S*SA804"}, {"remarkString": "  AUTH-APV/Y86286/000/USD251.96                   "}, {"remarkString": "BA-TCC859996"}, {"remarkString": "HTL-RATECODE-ABC"}, {"remarkString": "HTL-RATECODE-FHD"}, {"remarkString": "PNRID 2489235342"}, {"remarkString": "HTL-RATECODE-PP6"}, {"remarkString": "NO EMAIL"}, {"remarkString": "POSSIBLE DUPE BOOKING. SEE PNR QQBFAV QZGVFU QNSAGT"}, {"remarkString": "HTL-RATECODE-FHP"}, {"remarkString": "TRACEID AF48D471C821622C"}, {"remarkString": "AIR-SEQ1"}, {"remarkString": "  AUTH-CSC MATCHED/Y                              "}, {"remarkString": "PPT DOB-01/01/1993 TEST/INTEGRATION -M"}, {"remarkString": "POSSIBLE DUPE BOOKING. SEE PNR SNCPSN OLTLYM OLVJEM"}, {"remarkString": "IB-TCC859996"}, {"remarkString": "NO-COMMISSION-APPLIES-1737291398"}, {"remarkString": "TRAVELERPID E05CE264-A591-4AB5-A01E-E4AB84F45408"}], "travelerInfos": [{"airVendorCancellationInfo": {"airVendorCancellationObjects": []}, "createdMcos": [], "travelerIdx": 0, "userId": {"id": "e05ce264-a591-4ab5-a01e-e4ab84f45408"}, "paxType": "ADULT", "tickets": [{"ticketNumber": "0017093152905", "ticketType": "FLIGHT", "issuedDateTime": {"iso8601": "2025-01-19T00:00:00"}, "status": "ISSUED", "amount": {"base": {"amount": 205.92, "currencyCode": "USD", "convertedAmount": 205.92, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 46.04, "currencyCode": "USD", "convertedAmount": 46.04, "convertedCurrency": "USD", "otherCoinage": []}}, "flightCoupons": [{"legIdx": 0, "flightIdx": 0, "status": "NOT_FLOWN"}, {"legIdx": 1, "flightIdx": 0, "status": "NOT_FLOWN"}], "ancillaries": [], "validatingAirlineCode": "AA", "exchangePolicy": {"exchangePenalty": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}, "isExchangeable": true, "isCat16": false, "isConditional": true}, "refundPolicy": {"isRefundable": false, "isRefundableByObt": false, "isCat16": false, "isConditional": false}, "taxBreakdown": {"tax": [{"amount": {"amount": 10.4, "currencyCode": "USD", "convertedAmount": 10.4, "convertedCurrency": "USD", "otherCoinage": []}, "taxCode": "ZP"}, {"amount": {"amount": 11.2, "currencyCode": "USD", "convertedAmount": 11.2, "convertedCurrency": "USD", "otherCoinage": []}, "taxCode": "AY"}, {"amount": {"amount": 15.44, "currencyCode": "USD", "convertedAmount": 15.44, "convertedCurrency": "USD", "otherCoinage": []}, "taxCode": "US"}, {"amount": {"amount": 9, "currencyCode": "USD", "convertedAmount": 9, "convertedCurrency": "USD", "otherCoinage": []}, "taxCode": "XF"}]}, "commission": {"amount": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}, "percent": 0}, "iataNumber": "14640990", "fareCalculation": "SEA AA LAX Q0.17 114.42AA SEA Q0.17 91.16USD205.92END ZPSEALAXXFSEA4.5LAX4.5", "paymentDetails": [{"amount": {"base": {"amount": 205.92, "currencyCode": "USD", "convertedAmount": 205.92, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 46.04, "currencyCode": "USD", "convertedAmount": 46.04, "convertedCurrency": "USD", "otherCoinage": []}}, "fop": {"type": "CARD", "card": {"id": "ea624176-8fac-4b9c-bf88-872fcd7588b3", "type": "CREDIT", "company": "AMEX", "name": "<PERSON>", "address": {"addressLines": ["123 Elm st"], "administrativeArea": "", "administrativeAreaName": "", "description": "", "isDefault": false, "languageCode": "", "locality": "", "locationCode": "", "organization": "", "postalCode": "98199", "continentCode": "", "recipients": [], "regionCode": "US", "regionName": "", "revision": 0, "sortingCode": "", "sublocality": "", "timezone": ""}, "number": "3XXXXXXXXXXX0005", "expiryMonth": 1, "expiryYear": 2039, "cvv": "", "label": "Stripe Amex Test Card", "currency": "", "externalId": ""}, "additionalInfo": "", "accessType": {"accessType": "PERSONAL", "entityIds": ["e05ce264-a591-4ab5-a01e-e4ab84f45408"], "entities": [{"entityId": "e05ce264-a591-4ab5-a01e-e4ab84f45408", "centralCardAccessLevel": "UNKNOWN"}]}, "paymentMethod": "CREDIT_CARD"}, "isRefunded": false}], "ticketSettlement": "ARC_TICKET", "publishedFare": {"base": {"amount": 205.92, "currencyCode": "USD", "convertedAmount": 205.92, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 46.04, "currencyCode": "USD", "convertedAmount": 46.04, "convertedCurrency": "USD", "otherCoinage": []}}, "vendorCancellationId": "", "conjunctionTicketSuffix": [], "pcc": "1ZSK"}], "boardingPass": [], "booking": {"seats": [], "luggageDetails": [], "otherAncillaries": [], "itinerary": {"totalFare": {"base": {"amount": 205.92, "currencyCode": "USD", "convertedAmount": 205.92, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 46.04, "currencyCode": "USD", "convertedAmount": 46.04, "convertedCurrency": "USD", "otherCoinage": []}}, "totalFlightsFare": {"base": {"amount": 205.92, "currencyCode": "USD", "convertedAmount": 205.92, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 46.04, "currencyCode": "USD", "convertedAmount": 46.04, "convertedCurrency": "USD", "otherCoinage": []}}, "flightFareBreakup": [{"legIndices": [0, 1], "flightsFare": {"base": {"amount": 205.92, "currencyCode": "USD", "convertedAmount": 205.92, "convertedCurrency": "USD", "otherCoinage": []}, "tax": {"amount": 46.04, "currencyCode": "USD", "convertedAmount": 46.04, "convertedCurrency": "USD", "otherCoinage": []}}}], "fareComponents": [{"fareBasisCode": "NVAZZNM3", "tourCode": "", "ticketDesignator": "", "flightIds": [{"legIdx": 0, "flightIdx": 0}]}, {"fareBasisCode": "QVAKUSM3", "tourCode": "", "ticketDesignator": "", "flightIds": [{"legIdx": 1, "flightIdx": 0}]}], "otherAncillaryFares": []}, "otherCharges": []}, "appliedCredits": [], "specialServiceRequestInfos": []}], "automatedExchangeInfo": {"supportedExchanges": [{"legInfos": [{"legIdx": 0}, {"legIdx": 1}]}]}, "bookingMetadata": {"fareStatistics": {"statisticsItems": [{"statisticType": "MINIMUM", "totalFare": {"base": {"amount": 164.06, "currencyCode": "USD", "convertedAmount": 164.06, "convertedCurrency": "USD"}, "tax": {"amount": 62.3, "currencyCode": "USD", "convertedAmount": 62.3, "convertedCurrency": "USD"}}}, {"statisticType": "MEDIAN", "totalFare": {"base": {"amount": 534.3, "currencyCode": "USD", "convertedAmount": 534.3, "convertedCurrency": "USD"}, "tax": {"amount": 85.58, "currencyCode": "USD", "convertedAmount": 85.58, "convertedCurrency": "USD"}}}, {"statisticType": "MAXIMUM", "totalFare": {"base": {"amount": 4479.07, "currencyCode": "USD", "convertedAmount": 4479.07, "convertedCurrency": "USD"}, "tax": {"amount": 376.23, "currencyCode": "USD", "convertedAmount": 376.23, "convertedCurrency": "USD"}}}]}}, "otherServiceInfos": [{"customText": "*** <PERSON><PERSON><PERSON> TIME FOR AA6462Q 12APR LAXSEA  331P  638P", "flightIndexes": [{"flightIndex": 0, "legIndex": 0}, {"flightIndex": 0, "legIndex": 1}]}], "disruptedFlightDetails": [{"departureDateTime": {"iso8601": "2025-04-03T06:10:00"}, "arrivalDateTime": {"iso8601": "2025-04-03T08:59:00"}, "cabin": "ECONOMY", "originAirportCode": "SEA", "destinationAirportCode": "LAX", "marketing": {"num": "6420"}, "operating": {"num": "6420"}}, {"departureDateTime": {"iso8601": "2025-04-12T15:24:00"}, "arrivalDateTime": {"iso8601": "2025-04-12T18:32:00"}, "cabin": "ECONOMY", "originAirportCode": "LAX", "destinationAirportCode": "SEA", "marketing": {"num": "6462"}, "operating": {"num": "6462"}}]}, "additionalMetadata": {"airportInfo": [{"airportCode": "LAX", "airportName": "Los Angeles International Airport", "cityName": "Los Angeles", "countryName": "United States", "countryCode": "US", "zoneName": "America/Los_Angeles", "stateCode": "CA"}, {"airportCode": "SEA", "airportName": "Seattle–Tacoma International Airport", "cityName": "Seattle", "countryName": "United States", "countryCode": "US", "zoneName": "America/Los_Angeles", "stateCode": "WA"}], "airlineInfo": [{"airlineCode": "AA", "airlineName": "American Airlines"}]}, "preBookAnswers": {"answers": [], "preBookQuestionResponseId": ""}, "customFields": [], "bookingHistory": [{"bookerInfo": {"name": "OttoTest ApiUser", "email": "<EMAIL>", "role": "AGENT", "tmcName": ""}, "bookingInfo": {"updatedDateTime": {"iso8601": "2025-01-19T12:56:17Z"}, "status": "BOOKED", "bookingSourceClient": "WEB"}}], "totalFare": {"amount": 251.96, "currencyCode": "USD", "convertedAmount": 251.96, "convertedCurrency": "USD"}, "serviceFees": [], "paymentInfo": [{"fop": {"type": "CARD", "card": {"id": "ea624176-8fac-4b9c-bf88-872fcd7588b3", "type": "CREDIT", "company": "AMEX", "name": "<PERSON>", "address": {"addressLines": ["123 Elm st"], "administrativeArea": "", "administrativeAreaName": "", "description": "", "isDefault": false, "languageCode": "", "locality": "", "locationCode": "", "organization": "", "postalCode": "98199", "continentCode": "", "recipients": [], "regionCode": "US", "regionName": "", "revision": 0, "sortingCode": "", "sublocality": "", "timezone": ""}, "number": "3XXXXXXXXXXX0005", "expiryMonth": 1, "expiryYear": 2039, "cvv": "", "label": "Stripe Amex Test Card", "currency": "", "externalId": ""}, "additionalInfo": "", "accessType": {"accessType": "PERSONAL", "entityIds": ["e05ce264-a591-4ab5-a01e-e4ab84f45408"], "entities": [{"entityId": "e05ce264-a591-4ab5-a01e-e4ab84f45408", "centralCardAccessLevel": "UNKNOWN"}]}, "paymentMethod": "CREDIT_CARD"}, "totalCharge": {"amount": 251.96, "currencyCode": "USD", "convertedAmount": 251.96, "convertedCurrency": "USD", "otherCoinage": []}, "totalRefund": {"amount": 0, "currencyCode": "USD", "convertedAmount": 0, "convertedCurrency": "USD", "otherCoinage": []}, "netCharge": {"amount": 251.96, "currencyCode": "USD", "convertedAmount": 251.96, "convertedCurrency": "USD", "otherCoinage": []}}], "bookingStatus": "CONFIRMED_STATUS", "contactSupport": false, "travelerPnrVisibilityStatus": "VISIBLE", "pnrCreationDetails": {}, "approvalInfo": [{"approvalStatus": "APPROVAL_NOT_REQUIRED", "approvalType": "APPROVAL_TYPE_UNKNOWN"}], "tripId": "3984143638", "documents": [{"url": "https://duploservices-sboxmeta-payload-240774922464.s3.us-west-2.amazonaws.com/30981936-0a35-4371-8f45-a29078a0ab93/SPOT-US-001218.pdf?X-Amz-Security-Token=IQoJb3JpZ2luX2VjEFUaCXVzLXdlc3QtMiJGMEQCIHnBOM%2BMYzAoq5tWpwTWbctzMqiimJv6RKLuW3gn4%2FeKAiBIOB4Smv9kh%2B7YDdt0LKzELD%2BpafqCSyOuWpcW2tMnSirnAwhuEAEaDDI0MDc3NDkyMjQ2NCIManhWBWcBhTGwDn9NKsQDb1fleKfeWPeZAilW%2BlePOpfwQ5AR%2Bz1UdgGeJol7WPm%2B0MYt6pnQiD%2BE9C6fnIc7XHG1nOTUBXDSCSekelGdZzy1yDnENgKW3DBvcChIiqMjm4fuZfWn70OnNnLJHRosNsb688qxTXQ67Jf5X%2BcwxcNbJ6OiRn170BRR4EYDObSPneJopbObmDns2wb5DdAOKCBQoSI7WKXxJb8uSGkY5buyxJjDptSJmPgU10Cf22BB8RmXU7sm4A%2FOlpEeZMjMsdlzk29HwoQdorcTmesPItwcsvgpXMpqz7M%2FRiOHE%2FPNuPgWxRBzfcA8SKXwZ0clvMp1sq4ZewkpOsk%2BQE6OWTZ%2BDKTbW8Xhtb071UiajJ9DMa8JePovBmFp%2FmOzuzPhGqzEjTIH7LIA00pKOvYzvK8pwR0ZUvPyJLLaxonqUFNi3YsL%2FaSDH18KARWHQM4Ad0CyuC2tXtARjOsIwH2HqQMajEQKJb%2BVIBPmcdJvaFxHMb2esYKtYZtlQRGCNbnbO16xVy7haKCq%2BvmLlr3VxFtCORT6s9vKZVMwBxj3ubLdy9a8GVzkKN0HU8HjgkrG9WFatYUfnDvD1XqquEIHCP%2FI820wy6mWvQY6pgGHW7p4OwSce8DsbB%2BmRayjf8kLOJENJE54gksXrm6Vk8NvGADhGlIoEcZGU2%2F125%2Fzkw%2BozICBSJx6Dikhb8Ep8i88KoeMozTW%2BCDoGszcOouaDznsWup6homUrDVxvsWwpd%2BjKsZKPuirR4okP7nSxA95BMSlPf3giaiF5uSLucYbtU3I4i8DTv9MzYpXy1Ez7CKaOiEVXMauzqwAXj0equngnIX1&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250207T071632Z&X-Amz-SignedHeaders=host&X-Amz-Credential=ASIATQD2NYDQCKSFWHSK%2F20250207%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Expires=3600&X-Amz-Signature=a84c8745e1f29d550b37cbcd701dedd331f593ea1813bbff885ce584385aa931", "documentId": "76917a38-81f6-4053-83d5-19817452374d", "documentMetadata": {"documentType": "INVOICE", "entityType": "PNR", "entityId": "2489235342", "entityMetadata": {"pnrMetadata": {}, "invoiceMetadata": {"invoiceNumber": "SPOT-US-001218", "invoiceType": "FARE_INVOICE"}, "travelType": "AIR"}, "name": "SPOT-US-001218.pdf"}}], "pnrId": "2489235342", "invoiceInfos": [{"invoiceNumber": "SPOT-US-001218", "productType": "PNR", "invoiceId": "07c299db-9961-4b8d-98f1-ed71d0b06645"}], "totalFareAmount": {"base": {"amount": 205.92, "currencyCode": "USD", "convertedAmount": 205.92, "convertedCurrency": "USD"}, "tax": {"amount": 46.04, "currencyCode": "USD", "convertedAmount": 46.04, "convertedCurrency": "USD"}}, "dkNumber": "0000002558", "savingsFare": {"fareAmount": {"base": {"amount": 205.92, "currencyCode": "USD", "convertedAmount": 205.92, "convertedCurrency": "USD"}, "tax": {"amount": 46.04, "currencyCode": "USD", "convertedAmount": 46.04, "convertedCurrency": "USD"}}, "isTaxIncluded": true}, "tripUsageMetadata": {"tripUsageType": "STANDARD"}}, "operationSummary": {"pnrUpdateSummary": {}}}