"""
Unit tests for InTripAgent flight webhook field change tracking.
Tests the new meaningful field change tracking functionality.
"""

from unittest.mock import Mock

from in_trip.in_trip import <PERSON><PERSON>hange, InTripAgent, InTripFlightBooking, InTripFlightInfo
from server.database.models.chat_thread import ChatThread
from server.schemas.authenticate.user import User


class TestInTripFieldChanges:
    """Test suite for InTripAgent field change tracking functionality."""

    def setup_method(self):
        """Setup test fixtures."""
        # Create mock user and thread
        self.user = Mock(spec=User)
        self.user.id = "test-user-123"
        self.user.email = "<EMAIL>"

        self.thread = Mock(spec=ChatThread)
        self.thread.id = "test-thread-123"

        # Create InTripAgent instance
        self.agent = InTripAgent(self.user, self.thread)

        # Sample flight booking data
        self.sample_flight_booking = InTripFlightBooking(
            legs=[
                InTripFlightInfo(
                    leg_index=0,
                    confirmation_number="ABC123",
                    departure_time="2025-07-16T13:40:00",
                    departure_timezone="America/Los_Angeles",
                    arrival_time="2025-07-16T15:52:00",
                    arrival_timezone="America/Los_Angeles",
                    airline="AS",
                    flight_numbers=["1132"],
                    origin_name="Seattle–Tacoma International Airport",
                    origin_code="SEA",
                    destination_name="San Francisco International Airport",
                    destination_code="SFO",
                    price_string="$243.30",
                    seat="18C",
                    gate="A5",
                    terminal="North",
                    current_status="HK",
                )
            ]
        )

    def test_compare_and_update_flight_legs_departure_time_change(self):
        """Test detection and update of departure time changes."""
        # Webhook data with changed departure time
        webhook_legs = [
            {
                "flights": [
                    {
                        "departureDateTime": {"iso8601": "2025-07-16T14:00:00"},  # Changed from 13:40
                        "arrivalDateTime": {"iso8601": "2025-07-16T15:52:00"},
                        "departureGate": {"gate": "A5", "terminal": "North"},
                        "sourceStatus": "HK",
                    }
                ]
            }
        ]

        # Execute the function
        field_changes = self.agent._compare_and_update_flight_legs(self.sample_flight_booking, webhook_legs)

        # Verify results
        assert len(field_changes) == 1
        change = field_changes[0]

        assert change.field_name == "departure_time"
        assert change.old_value == "2025-07-16T13:40:00"
        assert change.new_value == "2025-07-16T14:00:00"

        # Verify leg was updated
        assert self.sample_flight_booking.legs[0].departure_time == "2025-07-16T14:00:00"

        # Verify aggregate fields were repopulated
        assert self.sample_flight_booking.departure_time == "2025-07-16T14:00:00"

    def test_compare_and_update_flight_legs_multiple_changes(self):
        """Test detection of multiple field changes in one update."""
        webhook_legs = [
            {
                "flights": [
                    {
                        "departureDateTime": {"iso8601": "2025-07-16T14:00:00"},  # Changed
                        "arrivalDateTime": {"iso8601": "2025-07-16T16:15:00"},  # Changed
                        "departureGate": {"gate": "B10", "terminal": "South"},  # Changed
                        "sourceStatus": "DL",  # Changed
                    }
                ]
            }
        ]

        field_changes = self.agent._compare_and_update_flight_legs(self.sample_flight_booking, webhook_legs)

        # Should detect 5 changes
        assert len(field_changes) == 5

        # Check each field change
        field_names = [change.field_name for change in field_changes]
        assert "departure_time" in field_names
        assert "arrival_time" in field_names
        assert "gate" in field_names
        assert "terminal" in field_names
        assert "flight_status" in field_names

        # Verify specific changes
        departure_change = next(c for c in field_changes if c.field_name == "departure_time")
        assert departure_change.old_value == "2025-07-16T13:40:00"
        assert departure_change.new_value == "2025-07-16T14:00:00"

        gate_change = next(c for c in field_changes if c.field_name == "gate")
        assert gate_change.old_value == "A5"
        assert gate_change.new_value == "B10"

    def test_compare_and_update_flight_legs_no_changes(self):
        """Test when webhook data matches current data (no changes)."""
        webhook_legs = [
            {
                "flights": [
                    {
                        "departureDateTime": {"iso8601": "2025-07-16T13:40:00"},  # Same
                        "arrivalDateTime": {"iso8601": "2025-07-16T15:52:00"},  # Same
                        "departureGate": {"gate": "A5", "terminal": "North"},  # Same
                        "sourceStatus": "HK",  # Same
                    }
                ]
            }
        ]

        field_changes = self.agent._compare_and_update_flight_legs(self.sample_flight_booking, webhook_legs)

        # Should detect no changes
        assert len(field_changes) == 0

    def test_compare_and_update_flight_legs_partial_webhook_data(self):
        """Test handling of partial webhook data (missing fields)."""
        webhook_legs = [
            {
                "flights": [
                    {
                        "departureDateTime": {"iso8601": "2025-07-16T14:00:00"},  # Changed
                        # Missing arrival time, gate, terminal, status
                    }
                ]
            }
        ]

        field_changes = self.agent._compare_and_update_flight_legs(self.sample_flight_booking, webhook_legs)

        # Should only detect departure time change
        assert len(field_changes) == 1
        assert field_changes[0].field_name == "departure_time"

    def test_compare_and_update_flight_legs_with_new_fields(self):
        """Test handling of new fields that weren't previously set."""
        # Start with a leg that has no gate/terminal
        self.sample_flight_booking.legs[0].gate = None
        self.sample_flight_booking.legs[0].terminal = None

        webhook_legs = [
            {
                "flights": [
                    {
                        "departureDateTime": {"iso8601": "2025-07-16T13:40:00"},  # Same
                        "arrivalDateTime": {"iso8601": "2025-07-16T15:52:00"},  # Same
                        "departureGate": {"gate": "A5", "terminal": "North"},  # New values
                        "sourceStatus": "HK",  # Same
                    }
                ]
            }
        ]

        field_changes = self.agent._compare_and_update_flight_legs(self.sample_flight_booking, webhook_legs)

        # Should detect gate and terminal changes
        assert len(field_changes) == 2
        field_names = [change.field_name for change in field_changes]
        assert "gate" in field_names
        assert "terminal" in field_names

        # Verify new values were set
        assert self.sample_flight_booking.legs[0].gate == "A5"
        assert self.sample_flight_booking.legs[0].terminal == "North"

    def test_field_change_meaningful_names(self):
        """Test that field changes use meaningful, user-friendly names."""
        webhook_legs = [
            {
                "flights": [
                    {
                        "departureDateTime": {"iso8601": "2025-07-16T14:00:00"},
                        "arrivalDateTime": {"iso8601": "2025-07-16T16:15:00"},
                        "departureGate": {"gate": "B10", "terminal": "South"},
                        "sourceStatus": "DL",
                    }
                ]
            }
        ]

        field_changes = self.agent._compare_and_update_flight_legs(self.sample_flight_booking, webhook_legs)

        # Check that field names are user-meaningful
        field_names = [change.field_name for change in field_changes]

        # Should NOT have technical names like "legs[0].departure_time"
        assert not any("legs[" in name for name in field_names)

        # Should have meaningful names
        expected_names = {"departure_time", "arrival_time", "gate", "terminal", "flight_status"}
        assert set(field_names) == expected_names

    def test_field_change_actual_values(self):
        """Test that field changes capture actual before/after values."""
        webhook_legs = [
            {
                "flights": [
                    {
                        "departureDateTime": {"iso8601": "2025-07-16T14:30:00"},
                        "departureGate": {"gate": "C7"},
                    }
                ]
            }
        ]

        field_changes = self.agent._compare_and_update_flight_legs(self.sample_flight_booking, webhook_legs)

        # Check departure time change
        departure_change = next(c for c in field_changes if c.field_name == "departure_time")
        assert departure_change.old_value == "2025-07-16T13:40:00"  # Original value
        assert departure_change.new_value == "2025-07-16T14:30:00"  # New value

        # Check gate change
        gate_change = next(c for c in field_changes if c.field_name == "gate")
        assert gate_change.old_value == "A5"  # Original value
        assert gate_change.new_value == "C7"  # New value

    def test_build_flight_change_summary_with_new_field_names(self):
        """Test that change summary works with new meaningful field names."""
        field_changes = [
            FieldChange(field_name="departure_time", old_value="2025-07-16T13:40:00", new_value="2025-07-16T14:00:00"),
            FieldChange(field_name="gate", old_value="A5", new_value="B10"),
        ]

        summary = self.agent._build_flight_change_summary(field_changes)

        # Should generate readable summary
        assert "Departure" in summary
        assert "Gate changed from A5 to B10" in summary

    def test_determine_change_type_with_new_field_names(self):
        """Test that change type determination works with new field names."""
        # Test schedule change
        schedule_changes = [FieldChange(field_name="departure_time", old_value="old", new_value="new")]
        assert self.agent._determine_change_type_from_changes(schedule_changes) == "schedule"

        # Test gate change
        gate_changes = [FieldChange(field_name="gate", old_value="A5", new_value="B10")]
        assert self.agent._determine_change_type_from_changes(gate_changes) == "gate"

        # Test status change
        status_changes = [FieldChange(field_name="flight_status", old_value="HK", new_value="DL")]
        assert self.agent._determine_change_type_from_changes(status_changes) == "status"
